import 'package:json_annotation/json_annotation.dart';

part 'category_model.g.dart';

@JsonSerializable()
class CategoryModel {
  final String id;
  final String name;
  final String description;
  final String iconUrl;
  final String? parentCategoryId;
  final List<String> subCategoryIds;
  final int itemCount;
  final bool isActive;
  final int sortOrder;
  final List<String> popularTags;
  final CategoryMetadata metadata;

  const CategoryModel({
    required this.id,
    required this.name,
    required this.description,
    required this.iconUrl,
    this.parentCategoryId,
    this.subCategoryIds = const [],
    this.itemCount = 0,
    this.isActive = true,
    this.sortOrder = 0,
    this.popularTags = const [],
    required this.metadata,
  });

  factory CategoryModel.fromJson(Map<String, dynamic> json) =>
      _$CategoryModelFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryModelToJson(this);

  bool get isMainCategory => parentCategoryId == null;
  bool get hasSubCategories => subCategoryIds.isNotEmpty;

  CategoryModel copyWith({
    String? id,
    String? name,
    String? description,
    String? iconUrl,
    String? parentCategoryId,
    List<String>? subCategoryIds,
    int? itemCount,
    bool? isActive,
    int? sortOrder,
    List<String>? popularTags,
    CategoryMetadata? metadata,
  }) {
    return CategoryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      parentCategoryId: parentCategoryId ?? this.parentCategoryId,
      subCategoryIds: subCategoryIds ?? this.subCategoryIds,
      itemCount: itemCount ?? this.itemCount,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      popularTags: popularTags ?? this.popularTags,
      metadata: metadata ?? this.metadata,
    );
  }
}

@JsonSerializable()
class CategoryMetadata {
  final String? color;
  final double? averagePrice;
  final List<String> commonBrands;
  final List<String> suggestedTags;
  final Map<String, String> customFields;

  const CategoryMetadata({
    this.color,
    this.averagePrice,
    this.commonBrands = const [],
    this.suggestedTags = const [],
    this.customFields = const {},
  });

  factory CategoryMetadata.fromJson(Map<String, dynamic> json) =>
      _$CategoryMetadataFromJson(json);

  Map<String, dynamic> toJson() => _$CategoryMetadataToJson(this);
}

// Predefined categories for the app
class AppCategories {
  static const List<Map<String, dynamic>> mainCategories = [
    {
      'id': 'electronics',
      'name': 'Electronics',
      'description': 'Cameras, laptops, gaming consoles, and more',
      'iconUrl': 'assets/icons/electronics.png',
      'color': '#2196F3',
    },
    {
      'id': 'tools',
      'name': 'Tools & Equipment',
      'description': 'Power tools, hand tools, construction equipment',
      'iconUrl': 'assets/icons/tools.png',
      'color': '#FF9800',
    },
    {
      'id': 'sports',
      'name': 'Sports & Recreation',
      'description': 'Bikes, camping gear, sports equipment',
      'iconUrl': 'assets/icons/sports.png',
      'color': '#4CAF50',
    },
    {
      'id': 'vehicles',
      'name': 'Vehicles',
      'description': 'Cars, motorcycles, boats, RVs',
      'iconUrl': 'assets/icons/vehicles.png',
      'color': '#F44336',
    },
    {
      'id': 'home',
      'name': 'Home & Garden',
      'description': 'Furniture, appliances, garden tools',
      'iconUrl': 'assets/icons/home.png',
      'color': '#9C27B0',
    },
    {
      'id': 'fashion',
      'name': 'Fashion & Accessories',
      'description': 'Designer clothes, jewelry, bags',
      'iconUrl': 'assets/icons/fashion.png',
      'color': '#E91E63',
    },
    {
      'id': 'events',
      'name': 'Events & Parties',
      'description': 'Party supplies, decorations, equipment',
      'iconUrl': 'assets/icons/events.png',
      'color': '#673AB7',
    },
    {
      'id': 'music',
      'name': 'Music & Audio',
      'description': 'Instruments, speakers, recording equipment',
      'iconUrl': 'assets/icons/music.png',
      'color': '#3F51B5',
    },
  ];

  static const Map<String, List<String>> subCategories = {
    'electronics': [
      'Cameras & Photography',
      'Laptops & Computers',
      'Gaming Consoles',
      'Smartphones & Tablets',
      'Audio Equipment',
      'TV & Projectors',
      'Drones',
      'Smart Home Devices',
    ],
    'tools': [
      'Power Tools',
      'Hand Tools',
      'Construction Equipment',
      'Automotive Tools',
      'Measuring Equipment',
      'Safety Equipment',
      'Generators',
      'Pressure Washers',
    ],
    'sports': [
      'Bicycles',
      'Camping & Hiking',
      'Water Sports',
      'Winter Sports',
      'Fitness Equipment',
      'Team Sports',
      'Golf Equipment',
      'Fishing Gear',
    ],
    'vehicles': [
      'Cars',
      'Motorcycles',
      'Boats',
      'RVs & Trailers',
      'ATVs',
      'Electric Vehicles',
      'Commercial Vehicles',
      'Vintage Vehicles',
    ],
    'home': [
      'Furniture',
      'Appliances',
      'Garden Tools',
      'Cleaning Equipment',
      'Kitchen Equipment',
      'Home Decor',
      'Storage Solutions',
      'Lighting',
    ],
    'fashion': [
      'Designer Clothing',
      'Jewelry',
      'Handbags',
      'Shoes',
      'Watches',
      'Sunglasses',
      'Formal Wear',
      'Vintage Fashion',
    ],
    'events': [
      'Party Decorations',
      'Sound Systems',
      'Lighting Equipment',
      'Tents & Canopies',
      'Tables & Chairs',
      'Catering Equipment',
      'Photo Booths',
      'Games & Entertainment',
    ],
    'music': [
      'Guitars',
      'Keyboards & Pianos',
      'Drums',
      'DJ Equipment',
      'Recording Equipment',
      'Speakers',
      'Microphones',
      'Orchestral Instruments',
    ],
  };
}
