import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';

class FirebaseService {
  static FirebaseService? _instance;
  static FirebaseService get instance => _instance ??= FirebaseService._();
  
  FirebaseService._();

  // Firebase instances
  FirebaseAuth get auth => FirebaseAuth.instance;
  FirebaseFirestore get firestore => FirebaseFirestore.instance;
  FirebaseStorage get storage => FirebaseStorage.instance;
  FirebaseMessaging get messaging => FirebaseMessaging.instance;
  FirebaseAnalytics get analytics => FirebaseAnalytics.instance;
  FirebaseCrashlytics get crashlytics => FirebaseCrashlytics.instance;

  // Collection references
  CollectionReference get users => firestore.collection('users');
  CollectionReference get products => firestore.collection('products');
  CollectionReference get rentals => firestore.collection('rentals');
  CollectionReference get reviews => firestore.collection('reviews');
  CollectionReference get conversations => firestore.collection('conversations');
  CollectionReference get messages => firestore.collection('messages');
  CollectionReference get categories => firestore.collection('categories');
  CollectionReference get notifications => firestore.collection('notifications');

  // Initialize Firebase
  static Future<void> initialize() async {
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        // These will be replaced with actual Firebase config
        apiKey: "AIzaSyBqTv0LfHHZ56yux_CN7X_LtvcHM3shjmQ",
        authDomain: "thriftify-d992a.firebaseapp.com",
        projectId: "thriftify-d992a",
        storageBucket: "thriftify-d992a.firebasestorage.app",
        messagingSenderId: "320799535254",
        appId: "1:320799535254:web:af9dd79fd5816419d512b7",
      ),
    );

    // Initialize Crashlytics
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

    // Set up messaging
    await _setupMessaging();
  }

  static Future<void> _setupMessaging() async {
    final messaging = FirebaseMessaging.instance;

    // Request permission for notifications
    await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    // Handle foreground messages
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      // Handle foreground message
      print('Received foreground message: ${message.notification?.title}');
    });

    // Handle message when app is opened from notification
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      // Handle notification tap
      print('App opened from notification: ${message.notification?.title}');
    });
  }

  // Get FCM token
  Future<String?> getFCMToken() async {
    try {
      return await messaging.getToken();
    } catch (e) {
      print('Error getting FCM token: $e');
      return null;
    }
  }

  // Subscribe to topic
  Future<void> subscribeToTopic(String topic) async {
    try {
      await messaging.subscribeToTopic(topic);
    } catch (e) {
      print('Error subscribing to topic $topic: $e');
    }
  }

  // Unsubscribe from topic
  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await messaging.unsubscribeFromTopic(topic);
    } catch (e) {
      print('Error unsubscribing from topic $topic: $e');
    }
  }

  // Log analytics event
  Future<void> logEvent(String name, Map<String, Object>? parameters) async {
    try {
      await analytics.logEvent(name: name, parameters: parameters);
    } catch (e) {
      print('Error logging analytics event: $e');
    }
  }

  // Set user properties for analytics
  Future<void> setUserProperties({
    required String userId,
    String? userType,
    String? location,
  }) async {
    try {
      await analytics.setUserId(id: userId);
      if (userType != null) {
        await analytics.setUserProperty(name: 'user_type', value: userType);
      }
      if (location != null) {
        await analytics.setUserProperty(name: 'location', value: location);
      }
    } catch (e) {
      print('Error setting user properties: $e');
    }
  }

  // Record error
  Future<void> recordError(dynamic exception, StackTrace? stackTrace) async {
    try {
      await crashlytics.recordError(exception, stackTrace);
    } catch (e) {
      print('Error recording crash: $e');
    }
  }

  // Set custom key for crashlytics
  Future<void> setCrashlyticsKey(String key, String value) async {
    try {
      await crashlytics.setCustomKey(key, value);
    } catch (e) {
      print('Error setting crashlytics key: $e');
    }
  }
}

// Background message handler
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  print('Handling background message: ${message.messageId}');
}
