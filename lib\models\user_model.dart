import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String displayName;
  final String? photoURL;
  final String? phoneNumber;
  final DateTime createdAt;
  final DateTime updatedAt;
  final UserProfile profile;
  final UserStats stats;
  final List<String> favoriteItems;
  final bool isVerified;
  final bool isActive;

  const UserModel({
    required this.id,
    required this.email,
    required this.displayName,
    this.photoURL,
    this.phoneNumber,
    required this.createdAt,
    required this.updatedAt,
    required this.profile,
    required this.stats,
    this.favoriteItems = const [],
    this.isVerified = false,
    this.isActive = true,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) =>
      _$UserModelFromJson(json);

  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? email,
    String? displayName,
    String? photoURL,
    String? phoneNumber,
    DateTime? createdAt,
    DateTime? updatedAt,
    UserProfile? profile,
    UserStats? stats,
    List<String>? favoriteItems,
    bool? isVerified,
    bool? isActive,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoURL: photoURL ?? this.photoURL,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      profile: profile ?? this.profile,
      stats: stats ?? this.stats,
      favoriteItems: favoriteItems ?? this.favoriteItems,
      isVerified: isVerified ?? this.isVerified,
      isActive: isActive ?? this.isActive,
    );
  }
}

@JsonSerializable()
class UserProfile {
  final String? bio;
  final String? location;
  final double? latitude;
  final double? longitude;
  final DateTime? dateOfBirth;
  final String? address;
  final String? city;
  final String? state;
  final String? zipCode;
  final String? country;
  final List<String> languages;
  final Map<String, String> socialLinks;

  const UserProfile({
    this.bio,
    this.location,
    this.latitude,
    this.longitude,
    this.dateOfBirth,
    this.address,
    this.city,
    this.state,
    this.zipCode,
    this.country,
    this.languages = const [],
    this.socialLinks = const {},
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFromJson(json);

  Map<String, dynamic> toJson() => _$UserProfileToJson(this);

  UserProfile copyWith({
    String? bio,
    String? location,
    double? latitude,
    double? longitude,
    DateTime? dateOfBirth,
    String? address,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    List<String>? languages,
    Map<String, String>? socialLinks,
  }) {
    return UserProfile(
      bio: bio ?? this.bio,
      location: location ?? this.location,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      address: address ?? this.address,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      languages: languages ?? this.languages,
      socialLinks: socialLinks ?? this.socialLinks,
    );
  }
}

@JsonSerializable()
class UserStats {
  final int itemsListed;
  final int itemsRented;
  final int totalRentals;
  final double averageRating;
  final int totalReviews;
  final double totalEarnings;
  final double totalSpent;
  final int completedTransactions;

  const UserStats({
    this.itemsListed = 0,
    this.itemsRented = 0,
    this.totalRentals = 0,
    this.averageRating = 0.0,
    this.totalReviews = 0,
    this.totalEarnings = 0.0,
    this.totalSpent = 0.0,
    this.completedTransactions = 0,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) =>
      _$UserStatsFromJson(json);

  Map<String, dynamic> toJson() => _$UserStatsToJson(this);

  UserStats copyWith({
    int? itemsListed,
    int? itemsRented,
    int? totalRentals,
    double? averageRating,
    int? totalReviews,
    double? totalEarnings,
    double? totalSpent,
    int? completedTransactions,
  }) {
    return UserStats(
      itemsListed: itemsListed ?? this.itemsListed,
      itemsRented: itemsRented ?? this.itemsRented,
      totalRentals: totalRentals ?? this.totalRentals,
      averageRating: averageRating ?? this.averageRating,
      totalReviews: totalReviews ?? this.totalReviews,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      totalSpent: totalSpent ?? this.totalSpent,
      completedTransactions: completedTransactions ?? this.completedTransactions,
    );
  }
}
