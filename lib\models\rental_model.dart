import 'package:json_annotation/json_annotation.dart';

part 'rental_model.g.dart';

@JsonSerializable()
class RentalModel {
  final String id;
  final String productId;
  final String renterId;
  final String ownerId;
  final DateTime startDate;
  final DateTime endDate;
  final RentalStatus status;
  final RentalPricing pricing;
  final RentalDelivery? delivery;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String? notes;
  final String? cancellationReason;
  final DateTime? confirmedAt;
  final DateTime? completedAt;
  final DateTime? cancelledAt;
  final List<RentalStatusUpdate> statusHistory;

  const RentalModel({
    required this.id,
    required this.productId,
    required this.renterId,
    required this.ownerId,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.pricing,
    this.delivery,
    required this.createdAt,
    required this.updatedAt,
    this.notes,
    this.cancellationReason,
    this.confirmedAt,
    this.completedAt,
    this.cancelledAt,
    this.statusHistory = const [],
  });

  factory RentalModel.fromJson(Map<String, dynamic> json) =>
      _$RentalModelFromJson(json);

  Map<String, dynamic> toJson() => _$RentalModelToJson(this);

  int get rentalDays {
    return endDate.difference(startDate).inDays + 1;
  }

  bool get isActive {
    return status == RentalStatus.confirmed || status == RentalStatus.ongoing;
  }

  bool get canBeCancelled {
    return status == RentalStatus.pending || 
           status == RentalStatus.confirmed;
  }

  RentalModel copyWith({
    String? id,
    String? productId,
    String? renterId,
    String? ownerId,
    DateTime? startDate,
    DateTime? endDate,
    RentalStatus? status,
    RentalPricing? pricing,
    RentalDelivery? delivery,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? notes,
    String? cancellationReason,
    DateTime? confirmedAt,
    DateTime? completedAt,
    DateTime? cancelledAt,
    List<RentalStatusUpdate>? statusHistory,
  }) {
    return RentalModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      renterId: renterId ?? this.renterId,
      ownerId: ownerId ?? this.ownerId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      pricing: pricing ?? this.pricing,
      delivery: delivery ?? this.delivery,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      completedAt: completedAt ?? this.completedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      statusHistory: statusHistory ?? this.statusHistory,
    );
  }
}

enum RentalStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('confirmed')
  confirmed,
  @JsonValue('ongoing')
  ongoing,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('disputed')
  disputed,
}

@JsonSerializable()
class RentalPricing {
  final double dailyRate;
  final double totalDays;
  final double subtotal;
  final double? securityDeposit;
  final double? deliveryFee;
  final double? serviceFee;
  final double? tax;
  final double total;
  final String currency;
  final List<RentalDiscount> discounts;

  const RentalPricing({
    required this.dailyRate,
    required this.totalDays,
    required this.subtotal,
    this.securityDeposit,
    this.deliveryFee,
    this.serviceFee,
    this.tax,
    required this.total,
    this.currency = 'USD',
    this.discounts = const [],
  });

  factory RentalPricing.fromJson(Map<String, dynamic> json) =>
      _$RentalPricingFromJson(json);

  Map<String, dynamic> toJson() => _$RentalPricingToJson(this);
}

@JsonSerializable()
class RentalDiscount {
  final String type; // 'weekly', 'monthly', 'first_time', 'promo_code'
  final String description;
  final double amount;
  final bool isPercentage;

  const RentalDiscount({
    required this.type,
    required this.description,
    required this.amount,
    this.isPercentage = false,
  });

  factory RentalDiscount.fromJson(Map<String, dynamic> json) =>
      _$RentalDiscountFromJson(json);

  Map<String, dynamic> toJson() => _$RentalDiscountToJson(this);
}

@JsonSerializable()
class RentalDelivery {
  final bool isDeliveryRequested;
  final String? deliveryAddress;
  final DateTime? deliveryTime;
  final DateTime? pickupTime;
  final String? deliveryInstructions;
  final RentalDeliveryStatus deliveryStatus;
  final RentalDeliveryStatus returnStatus;

  const RentalDelivery({
    required this.isDeliveryRequested,
    this.deliveryAddress,
    this.deliveryTime,
    this.pickupTime,
    this.deliveryInstructions,
    this.deliveryStatus = RentalDeliveryStatus.pending,
    this.returnStatus = RentalDeliveryStatus.pending,
  });

  factory RentalDelivery.fromJson(Map<String, dynamic> json) =>
      _$RentalDeliveryFromJson(json);

  Map<String, dynamic> toJson() => _$RentalDeliveryToJson(this);
}

enum RentalDeliveryStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('in_transit')
  inTransit,
  @JsonValue('delivered')
  delivered,
  @JsonValue('failed')
  failed,
}

@JsonSerializable()
class RentalStatusUpdate {
  final RentalStatus status;
  final DateTime timestamp;
  final String? notes;
  final String updatedBy;

  const RentalStatusUpdate({
    required this.status,
    required this.timestamp,
    this.notes,
    required this.updatedBy,
  });

  factory RentalStatusUpdate.fromJson(Map<String, dynamic> json) =>
      _$RentalStatusUpdateFromJson(json);

  Map<String, dynamic> toJson() => _$RentalStatusUpdateToJson(this);
}
