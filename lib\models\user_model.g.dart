// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserModel _$UserModelFromJson(Map<String, dynamic> json) => UserModel(
  id: json['id'] as String,
  email: json['email'] as String,
  displayName: json['displayName'] as String,
  photoURL: json['photoURL'] as String?,
  phoneNumber: json['phoneNumber'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  profile: UserProfile.fromJson(json['profile'] as Map<String, dynamic>),
  stats: UserStats.fromJson(json['stats'] as Map<String, dynamic>),
  favoriteItems:
      (json['favoriteItems'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  isVerified: json['isVerified'] as bool? ?? false,
  isActive: json['isActive'] as bool? ?? true,
);

Map<String, dynamic> _$UserModelToJson(UserModel instance) => <String, dynamic>{
  'id': instance.id,
  'email': instance.email,
  'displayName': instance.displayName,
  'photoURL': instance.photoURL,
  'phoneNumber': instance.phoneNumber,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
  'profile': instance.profile,
  'stats': instance.stats,
  'favoriteItems': instance.favoriteItems,
  'isVerified': instance.isVerified,
  'isActive': instance.isActive,
};

UserProfile _$UserProfileFromJson(Map<String, dynamic> json) => UserProfile(
  bio: json['bio'] as String?,
  location: json['location'] as String?,
  latitude: (json['latitude'] as num?)?.toDouble(),
  longitude: (json['longitude'] as num?)?.toDouble(),
  dateOfBirth: json['dateOfBirth'] == null
      ? null
      : DateTime.parse(json['dateOfBirth'] as String),
  address: json['address'] as String?,
  city: json['city'] as String?,
  state: json['state'] as String?,
  zipCode: json['zipCode'] as String?,
  country: json['country'] as String?,
  languages:
      (json['languages'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  socialLinks:
      (json['socialLinks'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ) ??
      const {},
);

Map<String, dynamic> _$UserProfileToJson(UserProfile instance) =>
    <String, dynamic>{
      'bio': instance.bio,
      'location': instance.location,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'dateOfBirth': instance.dateOfBirth?.toIso8601String(),
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'languages': instance.languages,
      'socialLinks': instance.socialLinks,
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
  itemsListed: (json['itemsListed'] as num?)?.toInt() ?? 0,
  itemsRented: (json['itemsRented'] as num?)?.toInt() ?? 0,
  totalRentals: (json['totalRentals'] as num?)?.toInt() ?? 0,
  averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
  totalReviews: (json['totalReviews'] as num?)?.toInt() ?? 0,
  totalEarnings: (json['totalEarnings'] as num?)?.toDouble() ?? 0.0,
  totalSpent: (json['totalSpent'] as num?)?.toDouble() ?? 0.0,
  completedTransactions: (json['completedTransactions'] as num?)?.toInt() ?? 0,
);

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
  'itemsListed': instance.itemsListed,
  'itemsRented': instance.itemsRented,
  'totalRentals': instance.totalRentals,
  'averageRating': instance.averageRating,
  'totalReviews': instance.totalReviews,
  'totalEarnings': instance.totalEarnings,
  'totalSpent': instance.totalSpent,
  'completedTransactions': instance.completedTransactions,
};
