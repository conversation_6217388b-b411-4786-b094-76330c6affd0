import 'package:json_annotation/json_annotation.dart';

part 'review_model.g.dart';

@JsonSerializable()
class ReviewModel {
  final String id;
  final String rentalId;
  final String reviewerId;
  final String revieweeId;
  final String productId;
  final ReviewType type;
  final double rating;
  final String? title;
  final String? comment;
  final List<String> imageUrls;
  final ReviewRatings detailedRatings;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isVerified;
  final bool isHelpful;
  final int helpfulCount;
  final String? ownerResponse;
  final DateTime? ownerResponseDate;

  const ReviewModel({
    required this.id,
    required this.rentalId,
    required this.reviewerId,
    required this.revieweeId,
    required this.productId,
    required this.type,
    required this.rating,
    this.title,
    this.comment,
    this.imageUrls = const [],
    required this.detailedRatings,
    required this.createdAt,
    required this.updatedAt,
    this.isVerified = false,
    this.isHelpful = false,
    this.helpfulCount = 0,
    this.ownerResponse,
    this.ownerResponseDate,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) =>
      _$ReviewModelFromJson(json);

  Map<String, dynamic> toJson() => _$ReviewModelToJson(this);

  ReviewModel copyWith({
    String? id,
    String? rentalId,
    String? reviewerId,
    String? revieweeId,
    String? productId,
    ReviewType? type,
    double? rating,
    String? title,
    String? comment,
    List<String>? imageUrls,
    ReviewRatings? detailedRatings,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isVerified,
    bool? isHelpful,
    int? helpfulCount,
    String? ownerResponse,
    DateTime? ownerResponseDate,
  }) {
    return ReviewModel(
      id: id ?? this.id,
      rentalId: rentalId ?? this.rentalId,
      reviewerId: reviewerId ?? this.reviewerId,
      revieweeId: revieweeId ?? this.revieweeId,
      productId: productId ?? this.productId,
      type: type ?? this.type,
      rating: rating ?? this.rating,
      title: title ?? this.title,
      comment: comment ?? this.comment,
      imageUrls: imageUrls ?? this.imageUrls,
      detailedRatings: detailedRatings ?? this.detailedRatings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isVerified: isVerified ?? this.isVerified,
      isHelpful: isHelpful ?? this.isHelpful,
      helpfulCount: helpfulCount ?? this.helpfulCount,
      ownerResponse: ownerResponse ?? this.ownerResponse,
      ownerResponseDate: ownerResponseDate ?? this.ownerResponseDate,
    );
  }
}

enum ReviewType {
  @JsonValue('product')
  product,
  @JsonValue('renter')
  renter,
  @JsonValue('owner')
  owner,
}

@JsonSerializable()
class ReviewRatings {
  final double? communication;
  final double? condition;
  final double? cleanliness;
  final double? accuracy;
  final double? value;
  final double? delivery;
  final double? overall;

  const ReviewRatings({
    this.communication,
    this.condition,
    this.cleanliness,
    this.accuracy,
    this.value,
    this.delivery,
    this.overall,
  });

  factory ReviewRatings.fromJson(Map<String, dynamic> json) =>
      _$ReviewRatingsFromJson(json);

  Map<String, dynamic> toJson() => _$ReviewRatingsToJson(this);

  double get averageRating {
    final ratings = [
      communication,
      condition,
      cleanliness,
      accuracy,
      value,
      delivery,
      overall,
    ].where((rating) => rating != null).cast<double>();
    
    if (ratings.isEmpty) return 0.0;
    return ratings.reduce((a, b) => a + b) / ratings.length;
  }
}

@JsonSerializable()
class ReviewSummary {
  final double averageRating;
  final int totalReviews;
  final Map<int, int> ratingDistribution; // star rating -> count
  final ReviewRatings averageDetailedRatings;
  final List<String> commonTags;
  final double recommendationPercentage;

  const ReviewSummary({
    required this.averageRating,
    required this.totalReviews,
    required this.ratingDistribution,
    required this.averageDetailedRatings,
    this.commonTags = const [],
    this.recommendationPercentage = 0.0,
  });

  factory ReviewSummary.fromJson(Map<String, dynamic> json) =>
      _$ReviewSummaryFromJson(json);

  Map<String, dynamic> toJson() => _$ReviewSummaryToJson(this);
}
