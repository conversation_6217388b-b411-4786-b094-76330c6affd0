import 'package:json_annotation/json_annotation.dart';

part 'product_model.g.dart';

@JsonSerializable()
class ProductModel {
  final String id;
  final String ownerId;
  final String title;
  final String description;
  final String category;
  final String subCategory;
  final List<String> imageUrls;
  final double pricePerDay;
  final double? pricePerWeek;
  final double? pricePerMonth;
  final double? securityDeposit;
  final ProductCondition condition;
  final ProductAvailability availability;
  final ProductLocation location;
  final List<String> tags;
  final ProductSpecs specs;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final bool isFeatured;
  final int viewCount;
  final int favoriteCount;
  final double averageRating;
  final int reviewCount;
  final List<String> unavailableDates;

  const ProductModel({
    required this.id,
    required this.ownerId,
    required this.title,
    required this.description,
    required this.category,
    required this.subCategory,
    required this.imageUrls,
    required this.pricePerDay,
    this.pricePerWeek,
    this.pricePerMonth,
    this.securityDeposit,
    required this.condition,
    required this.availability,
    required this.location,
    this.tags = const [],
    required this.specs,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.isFeatured = false,
    this.viewCount = 0,
    this.favoriteCount = 0,
    this.averageRating = 0.0,
    this.reviewCount = 0,
    this.unavailableDates = const [],
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) =>
      _$ProductModelFromJson(json);

  Map<String, dynamic> toJson() => _$ProductModelToJson(this);

  ProductModel copyWith({
    String? id,
    String? ownerId,
    String? title,
    String? description,
    String? category,
    String? subCategory,
    List<String>? imageUrls,
    double? pricePerDay,
    double? pricePerWeek,
    double? pricePerMonth,
    double? securityDeposit,
    ProductCondition? condition,
    ProductAvailability? availability,
    ProductLocation? location,
    List<String>? tags,
    ProductSpecs? specs,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isFeatured,
    int? viewCount,
    int? favoriteCount,
    double? averageRating,
    int? reviewCount,
    List<String>? unavailableDates,
  }) {
    return ProductModel(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      subCategory: subCategory ?? this.subCategory,
      imageUrls: imageUrls ?? this.imageUrls,
      pricePerDay: pricePerDay ?? this.pricePerDay,
      pricePerWeek: pricePerWeek ?? this.pricePerWeek,
      pricePerMonth: pricePerMonth ?? this.pricePerMonth,
      securityDeposit: securityDeposit ?? this.securityDeposit,
      condition: condition ?? this.condition,
      availability: availability ?? this.availability,
      location: location ?? this.location,
      tags: tags ?? this.tags,
      specs: specs ?? this.specs,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isFeatured: isFeatured ?? this.isFeatured,
      viewCount: viewCount ?? this.viewCount,
      favoriteCount: favoriteCount ?? this.favoriteCount,
      averageRating: averageRating ?? this.averageRating,
      reviewCount: reviewCount ?? this.reviewCount,
      unavailableDates: unavailableDates ?? this.unavailableDates,
    );
  }
}

enum ProductCondition {
  @JsonValue('new')
  newCondition,
  @JsonValue('like_new')
  likeNew,
  @JsonValue('good')
  good,
  @JsonValue('fair')
  fair,
  @JsonValue('poor')
  poor,
}

@JsonSerializable()
class ProductAvailability {
  final bool isAvailable;
  final DateTime? availableFrom;
  final DateTime? availableUntil;
  final int minRentalDays;
  final int maxRentalDays;
  final List<String> availableDays; // ['monday', 'tuesday', etc.]
  final String? availabilityNotes;

  const ProductAvailability({
    required this.isAvailable,
    this.availableFrom,
    this.availableUntil,
    this.minRentalDays = 1,
    this.maxRentalDays = 30,
    this.availableDays = const [],
    this.availabilityNotes,
  });

  factory ProductAvailability.fromJson(Map<String, dynamic> json) =>
      _$ProductAvailabilityFromJson(json);

  Map<String, dynamic> toJson() => _$ProductAvailabilityToJson(this);
}

@JsonSerializable()
class ProductLocation {
  final String address;
  final String city;
  final String state;
  final String zipCode;
  final String country;
  final double latitude;
  final double longitude;
  final double? deliveryRadius; // in kilometers
  final bool pickupAvailable;
  final bool deliveryAvailable;
  final double? deliveryFee;

  const ProductLocation({
    required this.address,
    required this.city,
    required this.state,
    required this.zipCode,
    required this.country,
    required this.latitude,
    required this.longitude,
    this.deliveryRadius,
    this.pickupAvailable = true,
    this.deliveryAvailable = false,
    this.deliveryFee,
  });

  factory ProductLocation.fromJson(Map<String, dynamic> json) =>
      _$ProductLocationFromJson(json);

  Map<String, dynamic> toJson() => _$ProductLocationToJson(this);
}

@JsonSerializable()
class ProductSpecs {
  final String? brand;
  final String? model;
  final String? color;
  final String? size;
  final String? weight;
  final String? dimensions;
  final int? yearManufactured;
  final Map<String, String> additionalSpecs;

  const ProductSpecs({
    this.brand,
    this.model,
    this.color,
    this.size,
    this.weight,
    this.dimensions,
    this.yearManufactured,
    this.additionalSpecs = const {},
  });

  factory ProductSpecs.fromJson(Map<String, dynamic> json) =>
      _$ProductSpecsFromJson(json);

  Map<String, dynamic> toJson() => _$ProductSpecsToJson(this);
}
