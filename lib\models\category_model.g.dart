// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'category_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CategoryModel _$CategoryModelFromJson(Map<String, dynamic> json) =>
    CategoryModel(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      iconUrl: json['iconUrl'] as String,
      parentCategoryId: json['parentCategoryId'] as String?,
      subCategoryIds:
          (json['subCategoryIds'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      itemCount: (json['itemCount'] as num?)?.toInt() ?? 0,
      isActive: json['isActive'] as bool? ?? true,
      sortOrder: (json['sortOrder'] as num?)?.toInt() ?? 0,
      popularTags:
          (json['popularTags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      metadata: CategoryMetadata.fromJson(
        json['metadata'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$CategoryModelToJson(CategoryModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'iconUrl': instance.iconUrl,
      'parentCategoryId': instance.parentCategoryId,
      'subCategoryIds': instance.subCategoryIds,
      'itemCount': instance.itemCount,
      'isActive': instance.isActive,
      'sortOrder': instance.sortOrder,
      'popularTags': instance.popularTags,
      'metadata': instance.metadata,
    };

CategoryMetadata _$CategoryMetadataFromJson(Map<String, dynamic> json) =>
    CategoryMetadata(
      color: json['color'] as String?,
      averagePrice: (json['averagePrice'] as num?)?.toDouble(),
      commonBrands:
          (json['commonBrands'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      suggestedTags:
          (json['suggestedTags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      customFields:
          (json['customFields'] as Map<String, dynamic>?)?.map(
            (k, e) => MapEntry(k, e as String),
          ) ??
          const {},
    );

Map<String, dynamic> _$CategoryMetadataToJson(CategoryMetadata instance) =>
    <String, dynamic>{
      'color': instance.color,
      'averagePrice': instance.averagePrice,
      'commonBrands': instance.commonBrands,
      'suggestedTags': instance.suggestedTags,
      'customFields': instance.customFields,
    };
