// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'rental_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RentalModel _$RentalModelFromJson(Map<String, dynamic> json) => RentalModel(
  id: json['id'] as String,
  productId: json['productId'] as String,
  renterId: json['renterId'] as String,
  ownerId: json['ownerId'] as String,
  startDate: DateTime.parse(json['startDate'] as String),
  endDate: DateTime.parse(json['endDate'] as String),
  status: $enumDecode(_$RentalStatusEnumMap, json['status']),
  pricing: RentalPricing.fromJson(json['pricing'] as Map<String, dynamic>),
  delivery: json['delivery'] == null
      ? null
      : RentalDelivery.fromJson(json['delivery'] as Map<String, dynamic>),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  notes: json['notes'] as String?,
  cancellationReason: json['cancellationReason'] as String?,
  confirmedAt: json['confirmedAt'] == null
      ? null
      : DateTime.parse(json['confirmedAt'] as String),
  completedAt: json['completedAt'] == null
      ? null
      : DateTime.parse(json['completedAt'] as String),
  cancelledAt: json['cancelledAt'] == null
      ? null
      : DateTime.parse(json['cancelledAt'] as String),
  statusHistory:
      (json['statusHistory'] as List<dynamic>?)
          ?.map((e) => RentalStatusUpdate.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$RentalModelToJson(RentalModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'renterId': instance.renterId,
      'ownerId': instance.ownerId,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'status': _$RentalStatusEnumMap[instance.status]!,
      'pricing': instance.pricing,
      'delivery': instance.delivery,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'notes': instance.notes,
      'cancellationReason': instance.cancellationReason,
      'confirmedAt': instance.confirmedAt?.toIso8601String(),
      'completedAt': instance.completedAt?.toIso8601String(),
      'cancelledAt': instance.cancelledAt?.toIso8601String(),
      'statusHistory': instance.statusHistory,
    };

const _$RentalStatusEnumMap = {
  RentalStatus.pending: 'pending',
  RentalStatus.confirmed: 'confirmed',
  RentalStatus.ongoing: 'ongoing',
  RentalStatus.completed: 'completed',
  RentalStatus.cancelled: 'cancelled',
  RentalStatus.disputed: 'disputed',
};

RentalPricing _$RentalPricingFromJson(Map<String, dynamic> json) =>
    RentalPricing(
      dailyRate: (json['dailyRate'] as num).toDouble(),
      totalDays: (json['totalDays'] as num).toDouble(),
      subtotal: (json['subtotal'] as num).toDouble(),
      securityDeposit: (json['securityDeposit'] as num?)?.toDouble(),
      deliveryFee: (json['deliveryFee'] as num?)?.toDouble(),
      serviceFee: (json['serviceFee'] as num?)?.toDouble(),
      tax: (json['tax'] as num?)?.toDouble(),
      total: (json['total'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'USD',
      discounts:
          (json['discounts'] as List<dynamic>?)
              ?.map((e) => RentalDiscount.fromJson(e as Map<String, dynamic>))
              .toList() ??
          const [],
    );

Map<String, dynamic> _$RentalPricingToJson(RentalPricing instance) =>
    <String, dynamic>{
      'dailyRate': instance.dailyRate,
      'totalDays': instance.totalDays,
      'subtotal': instance.subtotal,
      'securityDeposit': instance.securityDeposit,
      'deliveryFee': instance.deliveryFee,
      'serviceFee': instance.serviceFee,
      'tax': instance.tax,
      'total': instance.total,
      'currency': instance.currency,
      'discounts': instance.discounts,
    };

RentalDiscount _$RentalDiscountFromJson(Map<String, dynamic> json) =>
    RentalDiscount(
      type: json['type'] as String,
      description: json['description'] as String,
      amount: (json['amount'] as num).toDouble(),
      isPercentage: json['isPercentage'] as bool? ?? false,
    );

Map<String, dynamic> _$RentalDiscountToJson(RentalDiscount instance) =>
    <String, dynamic>{
      'type': instance.type,
      'description': instance.description,
      'amount': instance.amount,
      'isPercentage': instance.isPercentage,
    };

RentalDelivery _$RentalDeliveryFromJson(Map<String, dynamic> json) =>
    RentalDelivery(
      isDeliveryRequested: json['isDeliveryRequested'] as bool,
      deliveryAddress: json['deliveryAddress'] as String?,
      deliveryTime: json['deliveryTime'] == null
          ? null
          : DateTime.parse(json['deliveryTime'] as String),
      pickupTime: json['pickupTime'] == null
          ? null
          : DateTime.parse(json['pickupTime'] as String),
      deliveryInstructions: json['deliveryInstructions'] as String?,
      deliveryStatus:
          $enumDecodeNullable(
            _$RentalDeliveryStatusEnumMap,
            json['deliveryStatus'],
          ) ??
          RentalDeliveryStatus.pending,
      returnStatus:
          $enumDecodeNullable(
            _$RentalDeliveryStatusEnumMap,
            json['returnStatus'],
          ) ??
          RentalDeliveryStatus.pending,
    );

Map<String, dynamic> _$RentalDeliveryToJson(RentalDelivery instance) =>
    <String, dynamic>{
      'isDeliveryRequested': instance.isDeliveryRequested,
      'deliveryAddress': instance.deliveryAddress,
      'deliveryTime': instance.deliveryTime?.toIso8601String(),
      'pickupTime': instance.pickupTime?.toIso8601String(),
      'deliveryInstructions': instance.deliveryInstructions,
      'deliveryStatus': _$RentalDeliveryStatusEnumMap[instance.deliveryStatus]!,
      'returnStatus': _$RentalDeliveryStatusEnumMap[instance.returnStatus]!,
    };

const _$RentalDeliveryStatusEnumMap = {
  RentalDeliveryStatus.pending: 'pending',
  RentalDeliveryStatus.scheduled: 'scheduled',
  RentalDeliveryStatus.inTransit: 'in_transit',
  RentalDeliveryStatus.delivered: 'delivered',
  RentalDeliveryStatus.failed: 'failed',
};

RentalStatusUpdate _$RentalStatusUpdateFromJson(Map<String, dynamic> json) =>
    RentalStatusUpdate(
      status: $enumDecode(_$RentalStatusEnumMap, json['status']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      notes: json['notes'] as String?,
      updatedBy: json['updatedBy'] as String,
    );

Map<String, dynamic> _$RentalStatusUpdateToJson(RentalStatusUpdate instance) =>
    <String, dynamic>{
      'status': _$RentalStatusEnumMap[instance.status]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'notes': instance.notes,
      'updatedBy': instance.updatedBy,
    };
