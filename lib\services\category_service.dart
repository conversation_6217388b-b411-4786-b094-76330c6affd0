import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/category_model.dart';
import 'firebase_service.dart';

class CategoryService {
  final FirebaseFirestore _firestore = FirebaseService.instance.firestore;

  // Get all categories
  Future<List<CategoryModel>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection('categories')
          .where('isActive', isEqualTo: true)
          .orderBy('sortOrder')
          .get();

      return snapshot.docs
          .map((doc) => CategoryModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch categories: $e');
    }
  }

  // Get main categories (no parent)
  Future<List<CategoryModel>> getMainCategories() async {
    try {
      final snapshot = await _firestore
          .collection('categories')
          .where('isActive', isEqualTo: true)
          .where('parentCategoryId', isNull: true)
          .orderBy('sortOrder')
          .get();

      return snapshot.docs
          .map((doc) => CategoryModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch main categories: $e');
    }
  }

  // Get subcategories for a parent category
  Future<List<CategoryModel>> getSubCategories(String parentCategoryId) async {
    try {
      final snapshot = await _firestore
          .collection('categories')
          .where('isActive', isEqualTo: true)
          .where('parentCategoryId', isEqualTo: parentCategoryId)
          .orderBy('sortOrder')
          .get();

      return snapshot.docs
          .map((doc) => CategoryModel.fromJson({
                'id': doc.id,
                ...doc.data(),
              }))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch subcategories: $e');
    }
  }

  // Initialize default categories (call this once to populate Firestore)
  Future<void> initializeDefaultCategories() async {
    try {
      final batch = _firestore.batch();

      // Create main categories
      for (int i = 0; i < AppCategories.mainCategories.length; i++) {
        final categoryData = AppCategories.mainCategories[i];
        final categoryRef = _firestore.collection('categories').doc();
        
        final category = CategoryModel(
          id: categoryRef.id,
          name: categoryData['name'],
          description: categoryData['description'],
          iconUrl: categoryData['iconUrl'],
          sortOrder: i,
          metadata: CategoryMetadata(
            color: categoryData['color'],
          ),
        );

        batch.set(categoryRef, {
          ...category.toJson(),
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });

        // Create subcategories
        final subCategories = AppCategories.subCategories[categoryData['id']] ?? [];
        for (int j = 0; j < subCategories.length; j++) {
          final subCategoryRef = _firestore.collection('categories').doc();
          final subCategory = CategoryModel(
            id: subCategoryRef.id,
            name: subCategories[j],
            description: 'Subcategory of ${categoryData['name']}',
            iconUrl: categoryData['iconUrl'],
            parentCategoryId: categoryRef.id,
            sortOrder: j,
            metadata: CategoryMetadata(
              color: categoryData['color'],
            ),
          );

          batch.set(subCategoryRef, {
            ...subCategory.toJson(),
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
        }
      }

      await batch.commit();
    } catch (e) {
      throw Exception('Failed to initialize categories: $e');
    }
  }

  // Update category item count
  Future<void> updateCategoryItemCount(String categoryId, int increment) async {
    try {
      await _firestore.collection('categories').doc(categoryId).update({
        'itemCount': FieldValue.increment(increment),
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      throw Exception('Failed to update category item count: $e');
    }
  }

  // Get category by ID
  Future<CategoryModel?> getCategoryById(String categoryId) async {
    try {
      final doc = await _firestore.collection('categories').doc(categoryId).get();
      if (doc.exists) {
        return CategoryModel.fromJson({
          'id': doc.id,
          ...doc.data()!,
        });
      }
      return null;
    } catch (e) {
      throw Exception('Failed to fetch category: $e');
    }
  }
}

// Riverpod providers
final categoryServiceProvider = Provider<CategoryService>((ref) => CategoryService());

final mainCategoriesProvider = FutureProvider<List<CategoryModel>>((ref) async {
  final categoryService = ref.read(categoryServiceProvider);
  return await categoryService.getMainCategories();
});

final subCategoriesProvider = FutureProvider.family<List<CategoryModel>, String>((ref, parentId) async {
  final categoryService = ref.read(categoryServiceProvider);
  return await categoryService.getSubCategories(parentId);
});

final categoryProvider = FutureProvider.family<CategoryModel?, String>((ref, categoryId) async {
  final categoryService = ref.read(categoryServiceProvider);
  return await categoryService.getCategoryById(categoryId);
});
