// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'product_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProductModel _$ProductModelFromJson(Map<String, dynamic> json) => ProductModel(
  id: json['id'] as String,
  ownerId: json['ownerId'] as String,
  title: json['title'] as String,
  description: json['description'] as String,
  category: json['category'] as String,
  subCategory: json['subCategory'] as String,
  imageUrls: (json['imageUrls'] as List<dynamic>)
      .map((e) => e as String)
      .toList(),
  pricePerDay: (json['pricePerDay'] as num).toDouble(),
  pricePerWeek: (json['pricePerWeek'] as num?)?.toDouble(),
  pricePerMonth: (json['pricePerMonth'] as num?)?.toDouble(),
  securityDeposit: (json['securityDeposit'] as num?)?.toDouble(),
  condition: $enumDecode(_$ProductConditionEnumMap, json['condition']),
  availability: ProductAvailability.fromJson(
    json['availability'] as Map<String, dynamic>,
  ),
  location: ProductLocation.fromJson(json['location'] as Map<String, dynamic>),
  tags:
      (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  specs: ProductSpecs.fromJson(json['specs'] as Map<String, dynamic>),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isActive: json['isActive'] as bool? ?? true,
  isFeatured: json['isFeatured'] as bool? ?? false,
  viewCount: (json['viewCount'] as num?)?.toInt() ?? 0,
  favoriteCount: (json['favoriteCount'] as num?)?.toInt() ?? 0,
  averageRating: (json['averageRating'] as num?)?.toDouble() ?? 0.0,
  reviewCount: (json['reviewCount'] as num?)?.toInt() ?? 0,
  unavailableDates:
      (json['unavailableDates'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
);

Map<String, dynamic> _$ProductModelToJson(ProductModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'ownerId': instance.ownerId,
      'title': instance.title,
      'description': instance.description,
      'category': instance.category,
      'subCategory': instance.subCategory,
      'imageUrls': instance.imageUrls,
      'pricePerDay': instance.pricePerDay,
      'pricePerWeek': instance.pricePerWeek,
      'pricePerMonth': instance.pricePerMonth,
      'securityDeposit': instance.securityDeposit,
      'condition': _$ProductConditionEnumMap[instance.condition]!,
      'availability': instance.availability,
      'location': instance.location,
      'tags': instance.tags,
      'specs': instance.specs,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isActive': instance.isActive,
      'isFeatured': instance.isFeatured,
      'viewCount': instance.viewCount,
      'favoriteCount': instance.favoriteCount,
      'averageRating': instance.averageRating,
      'reviewCount': instance.reviewCount,
      'unavailableDates': instance.unavailableDates,
    };

const _$ProductConditionEnumMap = {
  ProductCondition.newCondition: 'new',
  ProductCondition.likeNew: 'like_new',
  ProductCondition.good: 'good',
  ProductCondition.fair: 'fair',
  ProductCondition.poor: 'poor',
};

ProductAvailability _$ProductAvailabilityFromJson(Map<String, dynamic> json) =>
    ProductAvailability(
      isAvailable: json['isAvailable'] as bool,
      availableFrom: json['availableFrom'] == null
          ? null
          : DateTime.parse(json['availableFrom'] as String),
      availableUntil: json['availableUntil'] == null
          ? null
          : DateTime.parse(json['availableUntil'] as String),
      minRentalDays: (json['minRentalDays'] as num?)?.toInt() ?? 1,
      maxRentalDays: (json['maxRentalDays'] as num?)?.toInt() ?? 30,
      availableDays:
          (json['availableDays'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      availabilityNotes: json['availabilityNotes'] as String?,
    );

Map<String, dynamic> _$ProductAvailabilityToJson(
  ProductAvailability instance,
) => <String, dynamic>{
  'isAvailable': instance.isAvailable,
  'availableFrom': instance.availableFrom?.toIso8601String(),
  'availableUntil': instance.availableUntil?.toIso8601String(),
  'minRentalDays': instance.minRentalDays,
  'maxRentalDays': instance.maxRentalDays,
  'availableDays': instance.availableDays,
  'availabilityNotes': instance.availabilityNotes,
};

ProductLocation _$ProductLocationFromJson(Map<String, dynamic> json) =>
    ProductLocation(
      address: json['address'] as String,
      city: json['city'] as String,
      state: json['state'] as String,
      zipCode: json['zipCode'] as String,
      country: json['country'] as String,
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      deliveryRadius: (json['deliveryRadius'] as num?)?.toDouble(),
      pickupAvailable: json['pickupAvailable'] as bool? ?? true,
      deliveryAvailable: json['deliveryAvailable'] as bool? ?? false,
      deliveryFee: (json['deliveryFee'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ProductLocationToJson(ProductLocation instance) =>
    <String, dynamic>{
      'address': instance.address,
      'city': instance.city,
      'state': instance.state,
      'zipCode': instance.zipCode,
      'country': instance.country,
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'deliveryRadius': instance.deliveryRadius,
      'pickupAvailable': instance.pickupAvailable,
      'deliveryAvailable': instance.deliveryAvailable,
      'deliveryFee': instance.deliveryFee,
    };

ProductSpecs _$ProductSpecsFromJson(Map<String, dynamic> json) => ProductSpecs(
  brand: json['brand'] as String?,
  model: json['model'] as String?,
  color: json['color'] as String?,
  size: json['size'] as String?,
  weight: json['weight'] as String?,
  dimensions: json['dimensions'] as String?,
  yearManufactured: (json['yearManufactured'] as num?)?.toInt(),
  additionalSpecs:
      (json['additionalSpecs'] as Map<String, dynamic>?)?.map(
        (k, e) => MapEntry(k, e as String),
      ) ??
      const {},
);

Map<String, dynamic> _$ProductSpecsToJson(ProductSpecs instance) =>
    <String, dynamic>{
      'brand': instance.brand,
      'model': instance.model,
      'color': instance.color,
      'size': instance.size,
      'weight': instance.weight,
      'dimensions': instance.dimensions,
      'yearManufactured': instance.yearManufactured,
      'additionalSpecs': instance.additionalSpecs,
    };
