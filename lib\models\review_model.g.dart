// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ReviewModel _$ReviewModelFromJson(Map<String, dynamic> json) => ReviewModel(
  id: json['id'] as String,
  rentalId: json['rentalId'] as String,
  reviewerId: json['reviewerId'] as String,
  revieweeId: json['revieweeId'] as String,
  productId: json['productId'] as String,
  type: $enumDecode(_$ReviewTypeEnumMap, json['type']),
  rating: (json['rating'] as num).toDouble(),
  title: json['title'] as String?,
  comment: json['comment'] as String?,
  imageUrls:
      (json['imageUrls'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  detailedRatings: ReviewRatings.fromJson(
    json['detailedRatings'] as Map<String, dynamic>,
  ),
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
  isVerified: json['isVerified'] as bool? ?? false,
  isHelpful: json['isHelpful'] as bool? ?? false,
  helpfulCount: (json['helpfulCount'] as num?)?.toInt() ?? 0,
  ownerResponse: json['ownerResponse'] as String?,
  ownerResponseDate: json['ownerResponseDate'] == null
      ? null
      : DateTime.parse(json['ownerResponseDate'] as String),
);

Map<String, dynamic> _$ReviewModelToJson(ReviewModel instance) =>
    <String, dynamic>{
      'id': instance.id,
      'rentalId': instance.rentalId,
      'reviewerId': instance.reviewerId,
      'revieweeId': instance.revieweeId,
      'productId': instance.productId,
      'type': _$ReviewTypeEnumMap[instance.type]!,
      'rating': instance.rating,
      'title': instance.title,
      'comment': instance.comment,
      'imageUrls': instance.imageUrls,
      'detailedRatings': instance.detailedRatings,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
      'isVerified': instance.isVerified,
      'isHelpful': instance.isHelpful,
      'helpfulCount': instance.helpfulCount,
      'ownerResponse': instance.ownerResponse,
      'ownerResponseDate': instance.ownerResponseDate?.toIso8601String(),
    };

const _$ReviewTypeEnumMap = {
  ReviewType.product: 'product',
  ReviewType.renter: 'renter',
  ReviewType.owner: 'owner',
};

ReviewRatings _$ReviewRatingsFromJson(Map<String, dynamic> json) =>
    ReviewRatings(
      communication: (json['communication'] as num?)?.toDouble(),
      condition: (json['condition'] as num?)?.toDouble(),
      cleanliness: (json['cleanliness'] as num?)?.toDouble(),
      accuracy: (json['accuracy'] as num?)?.toDouble(),
      value: (json['value'] as num?)?.toDouble(),
      delivery: (json['delivery'] as num?)?.toDouble(),
      overall: (json['overall'] as num?)?.toDouble(),
    );

Map<String, dynamic> _$ReviewRatingsToJson(ReviewRatings instance) =>
    <String, dynamic>{
      'communication': instance.communication,
      'condition': instance.condition,
      'cleanliness': instance.cleanliness,
      'accuracy': instance.accuracy,
      'value': instance.value,
      'delivery': instance.delivery,
      'overall': instance.overall,
    };

ReviewSummary _$ReviewSummaryFromJson(Map<String, dynamic> json) =>
    ReviewSummary(
      averageRating: (json['averageRating'] as num).toDouble(),
      totalReviews: (json['totalReviews'] as num).toInt(),
      ratingDistribution: (json['ratingDistribution'] as Map<String, dynamic>)
          .map((k, e) => MapEntry(int.parse(k), (e as num).toInt())),
      averageDetailedRatings: ReviewRatings.fromJson(
        json['averageDetailedRatings'] as Map<String, dynamic>,
      ),
      commonTags:
          (json['commonTags'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          const [],
      recommendationPercentage:
          (json['recommendationPercentage'] as num?)?.toDouble() ?? 0.0,
    );

Map<String, dynamic> _$ReviewSummaryToJson(ReviewSummary instance) =>
    <String, dynamic>{
      'averageRating': instance.averageRating,
      'totalReviews': instance.totalReviews,
      'ratingDistribution': instance.ratingDistribution.map(
        (k, e) => MapEntry(k.toString(), e),
      ),
      'averageDetailedRatings': instance.averageDetailedRatings,
      'commonTags': instance.commonTags,
      'recommendationPercentage': instance.recommendationPercentage,
    };
